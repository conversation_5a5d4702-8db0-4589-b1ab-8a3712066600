package com.example.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import java.time.LocalDateTime;

@Entity
@Table(name = "playlist_items",
    uniqueConstraints = @UniqueConstraint(
        name = "uq_playlist_program",
        columnNames = {"playlist_id", "program_id"}
    ))
@Getter
@Setter
public class PlaylistItem {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @ManyToOne
    @JoinColumn(name = "playlist_id", nullable = false)
    private Playlist playlist;

    @ManyToOne
    @JoinColumn(name = "program_id", nullable = false)
    private RadioProgram program;

    @Column(name = "display_order")
    private Integer displayOrder;

    @Column(name = "added_at")
    private LocalDateTime addedAt;
}